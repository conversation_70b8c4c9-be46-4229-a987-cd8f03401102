/**
 * Direct contract interaction utilities for ENS operations
 * This file provides functions to interact directly with ENS contracts using ABIs
 * without relying on backend API calls for transaction preparation
 */

import { ethers } from 'ethers';
import { namehash, isValidENSName, isV<PERSON>d<PERSON>hai<PERSON>, get<PERSON>hainId, getENSTokenId, getSigner } from './ens-utils';
import { FACTORY_CONTRACT_ADDRESS, FACTORY_CONTRACT_ABI } from './contracts/factory-contract';
import { getNameWrapperAddress, NAMEWRAPPER_CONTRACT_ABI } from './contracts/name-wrapper';

/**
 * Direct registrar creation using contract ABI
 * Returns both transaction and the created contract address
 */
export async function createRegistrarDirect(
  ensName: string,
  chain: string = 'sepolia',
  customSigner?: ethers.Signer
): Promise<{
  transaction: ethers.TransactionResponse;
  contractAddress?: string;
  receipt?: ethers.TransactionReceipt;
}> {
  if (!isValidENSName(ensName)) {
    throw new Error('Invalid ENS name format');
  }

  if (!isValid<PERSON>hain(chain)) {
    throw new Error('Invalid chain name');
  }

  // Get signer
  const signer = customSigner || await getSigner();
  
  // Create factory contract instance
  const factoryContract = new ethers.Contract(
    FACTORY_CONTRACT_ADDRESS,
    FACTORY_CONTRACT_ABI,
    signer
  );

  // Calculate parent node hash
  const parentNode = namehash(ensName.toLowerCase());

  console.log('Creating registrar for:', ensName);
  console.log('Parent node:', parentNode);
  console.log('Factory address:', FACTORY_CONTRACT_ADDRESS);

  // Execute the transaction
  const transaction = await factoryContract.createSubnameRegistrar(parentNode);
  
  console.log('Transaction sent:', transaction.hash);

  // Wait for transaction receipt
  const receipt = await transaction.wait();
  
  console.log('Transaction confirmed:', receipt?.hash);

  // Parse the SubnameContractCreated event to get the contract address
  let contractAddress: string | undefined;
  if (receipt && receipt.logs) {
    const iface = new ethers.Interface(FACTORY_CONTRACT_ABI);
    for (const log of receipt.logs) {
      try {
        const parsed = iface.parseLog(log);
        if (parsed && parsed.name === 'SubnameContractCreated') {
          contractAddress = parsed.args.contractAddress;
          console.log('Created contract address:', contractAddress);
          break;
        }
      } catch {
        // Skip logs that don't match our ABI
        continue;
      }
    }
  }

  if (!contractAddress) {
    console.warn('Could not extract contract address from transaction receipt');
  }

  return { transaction, contractAddress, receipt };
}

/**
 * Direct NameWrapper transfer using contract ABI
 */
export async function transferNameWrapperDirect(
  ensName: string,
  fromAddress: string,
  toAddress: string,
  chain: string = 'sepolia',
  customSigner?: ethers.Signer
): Promise<{
  transaction: ethers.TransactionResponse;
  receipt?: ethers.TransactionReceipt;
}> {
  if (!isValidENSName(ensName)) {
    throw new Error('Invalid ENS name format');
  }

  if (!ethers.isAddress(fromAddress)) {
    throw new Error('Invalid from address');
  }

  if (!ethers.isAddress(toAddress)) {
    throw new Error('Invalid to address');
  }

  if (!isValidChain(chain)) {
    throw new Error('Invalid chain name');
  }

  // Get signer
  const signer = customSigner || await getSigner();
  
  // Verify signer address matches fromAddress
  const signerAddress = await signer.getAddress();
  if (signerAddress.toLowerCase() !== fromAddress.toLowerCase()) {
    throw new Error(`Signer address ${signerAddress} does not match from address ${fromAddress}`);
  }

  // Get NameWrapper contract address and create instance
  const nameWrapperAddress = getNameWrapperAddress(chain);
  const nameWrapperContract = new ethers.Contract(
    nameWrapperAddress,
    NAMEWRAPPER_CONTRACT_ABI,
    signer
  );

  // Get the token ID for the ENS name
  const tokenId = getENSTokenId(ensName);

  console.log('Transferring ENS name:', ensName);
  console.log('From:', fromAddress);
  console.log('To:', toAddress);
  console.log('Token ID:', tokenId);
  console.log('NameWrapper address:', nameWrapperAddress);

  // Execute the transfer
  const transaction = await nameWrapperContract.safeTransferFrom(
    fromAddress,
    toAddress,
    BigInt(tokenId),
    1n, // amount = 1 for ENS transfers
    '0x' // empty data
  );

  console.log('Transfer transaction sent:', transaction.hash);

  // Wait for confirmation
  const receipt = await transaction.wait();
  
  console.log('Transfer transaction confirmed:', receipt?.hash);

  return { transaction, receipt };
}

/**
 * Check if user owns an ENS name in the NameWrapper
 */
export async function checkNameWrapperOwnership(
  ensName: string,
  userAddress: string,
  chain: string = 'sepolia',
  customProvider?: ethers.Provider
): Promise<{
  isOwner: boolean;
  actualOwner?: string;
  isWrapped: boolean;
}> {
  if (!isValidENSName(ensName)) {
    throw new Error('Invalid ENS name format');
  }

  if (!ethers.isAddress(userAddress)) {
    throw new Error('Invalid user address');
  }

  // Get provider
  const provider = customProvider || new ethers.JsonRpcProvider(
    chain === 'mainnet' ? 'https://eth.llamarpc.com' : 'https://sepolia.infura.io/v3/YOUR_INFURA_KEY'
  );

  // Get NameWrapper contract
  const nameWrapperAddress = getNameWrapperAddress(chain);
  const nameWrapperContract = new ethers.Contract(
    nameWrapperAddress,
    NAMEWRAPPER_CONTRACT_ABI,
    provider
  );

  const tokenId = getENSTokenId(ensName);

  try {
    // Check if the name is wrapped (has a balance > 0)
    const balance = await nameWrapperContract.balanceOf(nameWrapperAddress, BigInt(tokenId));
    const isWrapped = balance > 0n;

    if (!isWrapped) {
      return { isOwner: false, isWrapped: false };
    }

    // Get the actual owner
    const actualOwner = await nameWrapperContract.ownerOf(BigInt(tokenId));
    const isOwner = actualOwner.toLowerCase() === userAddress.toLowerCase();

    return { isOwner, actualOwner, isWrapped };

  } catch (error) {
    console.error('Error checking NameWrapper ownership:', error);
    return { isOwner: false, isWrapped: false };
  }
}

/**
 * Get registrar contracts created by a user
 */
export async function getUserRegistrarContracts(
  userAddress: string,
  chain: string = 'sepolia',
  customProvider?: ethers.Provider
): Promise<string[]> {
  if (!ethers.isAddress(userAddress)) {
    throw new Error('Invalid user address');
  }

  // Get provider
  const provider = customProvider || new ethers.JsonRpcProvider(
    chain === 'mainnet' ? 'https://eth.llamarpc.com' : 'https://sepolia.infura.io/v3/YOUR_INFURA_KEY'
  );

  // Create factory contract instance
  const factoryContract = new ethers.Contract(
    FACTORY_CONTRACT_ADDRESS,
    FACTORY_CONTRACT_ABI,
    provider
  );

  try {
    const contracts = await factoryContract.getContractsByOwner(userAddress);
    return contracts;
  } catch (error) {
    console.error('Error getting user registrar contracts:', error);
    return [];
  }
}

/**
 * Validate ENS name ownership before operations
 */
export async function validateENSOwnership(
  ensName: string,
  userAddress: string,
  chain: string = 'sepolia',
  customProvider?: ethers.Provider
): Promise<{
  isValid: boolean;
  error?: string;
  ownershipType?: 'registry' | 'namewrapper';
  actualOwner?: string;
}> {
  try {
    // First check NameWrapper ownership
    const nameWrapperCheck = await checkNameWrapperOwnership(ensName, userAddress, chain, customProvider);
    
    if (nameWrapperCheck.isWrapped) {
      return {
        isValid: nameWrapperCheck.isOwner,
        error: nameWrapperCheck.isOwner ? undefined : `ENS name is owned by ${nameWrapperCheck.actualOwner}`,
        ownershipType: 'namewrapper',
        actualOwner: nameWrapperCheck.actualOwner
      };
    }

    // If not wrapped, check registry ownership
    const { getENSOwner } = await import('./ens-utils');
    const registryOwner = await getENSOwner(ensName, customProvider, getChainId(chain));
    
    if (!registryOwner) {
      return {
        isValid: false,
        error: 'ENS name not found or has no owner'
      };
    }

    const isRegistryOwner = registryOwner.toLowerCase() === userAddress.toLowerCase();
    
    return {
      isValid: isRegistryOwner,
      error: isRegistryOwner ? undefined : `ENS name is owned by ${registryOwner}`,
      ownershipType: 'registry',
      actualOwner: registryOwner
    };

  } catch (error) {
    return {
      isValid: false,
      error: `Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}
